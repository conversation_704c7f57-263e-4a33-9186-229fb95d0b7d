# Generated by Django 5.2.1 on 2025-07-24 14:35

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lca', '0087_emissionsourcedataset_source_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='emissionsource',
            name='description',
        ),
        migrations.RemoveField(
            model_name='emissionsource',
            name='type',
        ),
        migrations.RemoveField(
            model_name='emissionsource',
            name='update_time',
        ),
        migrations.AddField(
            model_name='emissionsource',
            name='creator',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='emissionsource',
            name='amount',
            field=models.DecimalField(decimal_places=5, default=0, max_digits=20, verbose_name='数量'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='emissionsource',
            name='category',
            field=models.ForeignKey(default='C', on_delete=django.db.models.deletion.CASCADE, to='lca.category', verbose_name='类别'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='emissionsource',
            name='geography',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lca.geography', verbose_name='适用的地理区域'),
        ),
        migrations.AlterField(
            model_name='emissionsource',
            name='name',
            field=models.JSONField(default=dict, verbose_name='名称'),
        ),
        migrations.AlterField(
            model_name='emissionsource',
            name='source',
            field=models.JSONField(max_length=255, verbose_name='数据来源'),
        ),
        migrations.AlterField(
            model_name='emissionsource',
            name='unit',
            field=models.ForeignKey(default='kg', on_delete=django.db.models.deletion.CASCADE, to='lca.unit', verbose_name='单位'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='emissionsource',
            name='uuid',
            field=models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
        ),
        migrations.AlterField(
            model_name='emissionsource',
            name='year',
            field=models.CharField(default=2025, max_length=255, verbose_name='时间代表性'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='emissionsourceapplication',
            name='creator',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AlterField(
            model_name='emissionsourceapplication',
            name='status',
            field=models.CharField(choices=[('ongoing', '审核中'), ('approved', '通过'), ('rejected', '未通过')], max_length=255, verbose_name='状态'),
        ),
        migrations.AlterField(
            model_name='emissionsourceinput',
            name='emission_source',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='inputs', to='lca.emissionsource', verbose_name='排放源'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='copyright',
            field=models.CharField(max_length=255, null=True, verbose_name='版权信息'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='generate_contact',
            field=models.CharField(max_length=255, verbose_name='数据生成联系人'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='inputer_contact',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='数据录入者'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='license_type',
            field=models.CharField(max_length=255, null=True, verbose_name='许可类型'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='owener',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='数据拥有者'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='publish',
            field=models.CharField(max_length=255, null=True, verbose_name='发布信息'),
        ),
        migrations.AlterField(
            model_name='emissionsourcemanagementapplication',
            name='view',
            field=models.CharField(max_length=255, null=True, verbose_name='访问权限信息'),
        ),
    ]
