import uuid
from django.db import models
from django.db.models import QuerySet
from lca.file.models import File
from django.conf import settings


class BOUNDARIES(models.TextChoices):
    """边界范围"""

    CRADLE_GATE = "cradle-gate", "从摇篮到大门"
    CRADLE_GRAVE = "cradle-grave", "从摇篮到坟墓"


class MASS_UNITS(models.TextChoices):
    """质量单位"""

    KG = "kg", "千克"
    T = "t", "吨"


class LIFE_CYCLE_STAGES(models.TextChoices):
    """生命周期阶段"""

    RAW_MATERIALS = "raw-materials", "原材料获取阶段"
    PRODUCTION = "production", "生产阶段"
    USE = "use", "使用阶段"
    DISTRIBUTION = "distribution", "分销阶段"
    DISPOSAL = "disposal", "废弃阶段"


class SOURCE_TYPES(models.TextChoices):
    """单元过程输入类型"""

    PROCESS = "process", "单元过程"
    EMISSION_SOURCE = "emission-source", "排放因子"
    CUSTOM_FACTOR = "custom-factor", "自定义因子"


class OUTPUT_TYPES(models.TextChoices):
    """单元过程输出类型"""

    MAIN_PRODUCT = "main-product", "主产品"
    BY_PRODUCT = "by-product", "共生产品"
    ENVIRONMENTAL_EMISSION = "environmental-emission", "直接排放"
    WASTE_FLOW = "waste", "待处置废弃物"


class UNIT_GROUPS(models.TextChoices):
    """单位分组"""

    ITEM = "item", "个数"
    MASS = "mass", "质量"
    ENGERY = "engery", "能量"
    LENGTH = "length", "长度"
    VOLUME = "volume", "体积"
    AREA = "area", "面积"


class UNITS(models.TextChoices):
    g = "g", "g"
    kg = "kg", "kg"
    t = "t", "t"
    kJ = "kJ", "kJ"
    MJ = "MJ", "MJ"
    GJ = "GJ", "GJ"
    kWh = "kWh", "kWh"
    MWh = "MWh", "MWh"
    L = "L", "L"
    m3 = "m3", "m3"
    items = "item(s)", "item(s)"
    pieces = "Piece(s)", "Piece(s)"


class Unit(models.Model):
    """
    计量单位表，如 kg, kWh, L, etc.
    """

    id = models.CharField(max_length=50, primary_key=True, verbose_name="单位名称")
    uuid = models.UUIDField(default=uuid.uuid4, editable=False)
    type = models.CharField(choices=UNIT_GROUPS.choices, null=False)
    symbol = models.CharField(max_length=50, verbose_name="单位符号")
    conversion_factor = models.DecimalField(
        max_digits=18, decimal_places=8, null=True, blank=True, verbose_name="转换因子"
    )

    def __str__(self):
        return f"{self.id}"


class DIRECTIONS(models.TextChoices):
    """方向"""

    INPUT = "input", "INPUT"
    OUTPUT = "output", "OUTPUT"


class CUSTOM_FACTOR_SOURCE(models.TextChoices):
    """自定义因子来源"""

    LITERATURE = "literature", "文献报告"
    INDUSTRY = "industry", "行业数据"
    MEASURED = "measured", "实测数据"
    THEORETICAL = "theoretical", "理论计算"
    OTHER = "other", "其他"


class PRODUCT(models.TextChoices):
    """九大产品"""

    CEMENT = "cement", "通用硅酸盐水泥"
    STEEL_SHEET = "steel-sheet", "钢铁-板材"
    STEEL_WIRE_ROD = "steel-wire-rod", "钢铁-线材"
    PHOTOVOLTAIC = "photovoltaic", "光伏组件"
    ELECTRIC_VEHICLE = "electric_vehicle", "轻型电动汽车"
    POWER_BATTERY = "power_battery", "动力电池"
    GLASS = "glass", "平板玻璃"
    ELECTROLYTIC_ALUMINUM = "electrolytic_aluminum", "电解铝"
    ETHYLENE = "ethylene", "乙烯"
    SYNTHETIC_AMMONIA = "synthetic_ammonia", "合成氨"


class CERTIFICATE_PRODUCT(models.TextChoices):
    """认证相关产品"""

    LITHIUM_BATTERY = "lithium_battery", "锂电池"
    PHOTOVOLTAIC = "photovoltaic", "光伏产品"
    STEEL = "steel", "钢铁"
    TEXTILE = "textile", "纺织品"
    ELECTRONICS = "electronics", "电子电器"
    TIRE = "tire", "轮胎"
    CEMENT = "cement", "水泥"
    ELECTROLYTIC_ALUMINUM = "electrolytic_aluminum", "电解铝"
    PHOSPHATE_FERTILIZER = "phosphate_fertilizer", "磷铵"
    WOOD_PRODUCT = "wood_product", "木制品"
    ELECTRIC_VEHICLE = "electric_vehicle", "电动汽车"
    POWER_BATTERY = "power_battery", "动力电池"
    GLASS = "glass", "玻璃"
    ETHYLENE = "ethylene", "乙烯"
    SYNTHETIC_AMMONIA = "synthetic_ammonia", "合成氨"


class Geography(models.Model):
    """
    地理区域表
    """

    id = models.CharField(max_length=50, primary_key=True, verbose_name="缩写")
    name = models.CharField(max_length=50, verbose_name="地理区域名称")
    parent = models.ForeignKey(
        "self",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name="上级地理区域",
        related_name="children",
    )
    coord = models.CharField(max_length=50, null=True, blank=True, verbose_name="经纬度坐标")
    classification = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name="地理区域分类(如 'Country', 'Province', 'City')",
    )

    def __str__(self):
        return f"{self.name}"


class INPUT_TYPES(models.TextChoices):
    """排放类型"""

    RAW_MATERIAL = "raw-material", "原辅料"
    ENERGY = "energy", "能源"
    TRANSPORT = "transport", "运输"


class EmissionTypes(models.TextChoices):
    """排放类型"""

    RAW_MATERIAL = "raw-material", "原辅料"
    ENERGY = "energy", "能源"
    NATURAL_RESOURCE = "natural-resource", "自然资源"
    PACKAGING = "packaging", "包装"
    TRANSPORT = "transport", "运输"
    INFRASTRUCTURE = "infrastructure", "基础设备设施"
    RECYCLED_MATERIAL = "recycle-material", "再生原料"
    MATERIAL_CONTENT = "material-content", "材料/成分含量"


class ALLOCATION_METHODS(models.TextChoices):
    """分配方法"""

    SYSTEM_EXPANSION = "system-expansion", "系统扩展法"
    MASS = "mass", "质量分配"
    ENERGY = "energy", "热值分配"
    ECONOMIC = "economic", "经济价值分配"
    OTHER = "other", "其他分配方法"
    NO = "no", "无需分配"


class EmissionSource(models.Model):
    """
    排放源表，定义排放因子所对应的活动场景
    """

    uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, null=False, blank=False)
    co2e = models.DecimalField(
        decimal_places=5,
        max_digits=20,
        verbose_name="CO2e",
        null=False,
        blank=False,
        default=0,
    )
    name = models.JSONField(default=dict, verbose_name="名称", null=False, blank=False)
    amount = models.DecimalField(decimal_places=5, max_digits=20, null=False, blank=False, verbose_name="数量")
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, verbose_name="单位", null=False, blank=False)
    geography = models.ForeignKey(Geography, on_delete=models.CASCADE, verbose_name="适用的地理区域", null=False, blank=False)
    category = models.ForeignKey("Category", on_delete=models.CASCADE, verbose_name="类别", null=False, blank=False)
    source = models.JSONField(max_length=255, verbose_name="数据来源", null=False, blank=False)
    year = models.CharField(max_length=255, verbose_name="时间代表性", null=False, blank=False)
    creator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name="创建者",
        null=False,
        blank=False,
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)


class Category(models.Model):
    """
    类别
    """

    id = models.CharField(max_length=30, primary_key=True, verbose_name="ID")
    name = models.CharField(max_length=100, verbose_name="类别名称", null=False, blank=False)
    parent = models.ForeignKey(
        "self",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name="上级",
        related_name="children",
    )

    def __str__(self):
        return self.name


class Model(models.Model):
    """
    模型
    """

    plateform = models.BooleanField(verbose_name="是否平台模型", default=False, db_index=True)
    parent_id = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="父模型，用于记录历史记录",
        related_name="children",
    )
    product = models.CharField(
        max_length=100,
        verbose_name="产品",
        null=True,
        blank=False,
        choices=PRODUCT.choices,
    )
    uuid = models.UUIDField(default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, verbose_name="产品名称", null=False, blank=False)
    functional_unit = models.CharField(max_length=100, verbose_name="功能单位", null=False, blank=False)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name="类别", null=False, blank=False)
    specs = models.CharField(max_length=100, verbose_name="产品型号", null=False, blank=False)
    company_name = models.CharField(max_length=100, verbose_name="公司名称", null=True, blank=True)
    description = models.CharField(max_length=200, verbose_name="描述", null=True, blank=True)

    boundary = models.CharField(
        choices=BOUNDARIES.choices,
        max_length=50,
        verbose_name="系统边界",
        null=False,
        blank=False,
    )
    year = models.IntegerField(verbose_name="年份", null=False, blank=False)
    geography = models.ForeignKey(
        Geography,
        on_delete=models.CASCADE,
        verbose_name="产品产地",
        null=False,
        blank=False,
    )
    rule = models.CharField(verbose_name="取舍规则", null=True, blank=True, max_length=200)
    amount = models.DecimalField(decimal_places=5, max_digits=20, verbose_name="产量", null=False, blank=False)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, verbose_name="单位", null=False, blank=False)
    co2e = models.DecimalField(
        verbose_name="二氧化碳当量",
        max_digits=20,
        decimal_places=5,
        null=False,
        blank=False,
        default=0,
    )
    creator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name="创建者",
        null=False,
        blank=False,
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)

    report = models.ForeignKey(
        File,
        on_delete=models.CASCADE,
        verbose_name="报告",
        null=True,
        blank=True,
        related_name="reportModel",
    )
    image = models.ForeignKey(
        File,
        on_delete=models.CASCADE,
        verbose_name="图谱",
        null=True,
        blank=True,
        related_name="imageModel",
    )
    analysis_report = models.ForeignKey(
        File,
        on_delete=models.CASCADE,
        verbose_name="分析报告",
        null=True,
        blank=True,
        related_name="analysisReportModel",
    )


class Calculater(models.Model):
    """
    输出计算器
    """

    name = models.CharField(max_length=200, verbose_name="公式名称", null=False, blank=False)
    formulas = models.ManyToManyField("Formula", related_name="formulas")


class Formula(models.Model):
    """
    公式
    """

    name = models.CharField(max_length=200, verbose_name="公式名称", null=False, blank=False)
    class_path = models.CharField(max_length=200, verbose_name="类路径", null=False, blank=False)

    @property
    def params(self):
        from lca.accounting.formulas import get_formula_info

        return get_formula_info(self.class_path)


class OutputFormula(models.Model):
    """
    计算器和公式对应关系
    """

    calculater = models.ForeignKey(
        Calculater,
        on_delete=models.CASCADE,
        verbose_name="计算器",
        null=False,
        blank=False,
    )
    formula = models.ForeignKey(Formula, on_delete=models.CASCADE, verbose_name="公式", null=False, blank=False)

    class Meta:
        unique_together = ("calculater", "formula")


class LifeCycle(models.Model):
    """
    生命周期
    """

    model = models.ForeignKey(
        Model,
        on_delete=models.CASCADE,
        verbose_name="模型",
        null=False,
        blank=False,
        related_name="life_cycles",
        db_index=True,
    )
    stage = models.CharField(
        choices=LIFE_CYCLE_STAGES.choices,
        max_length=50,
        verbose_name="生命周期阶段",
        null=False,
        blank=False,
        default="production",
    )
    process = models.ForeignKey("Process", on_delete=models.CASCADE, null=True, blank=False, db_index=True)
    co2e = models.DecimalField(
        decimal_places=5,
        max_digits=20,
        verbose_name="CO2e",
        null=False,
        blank=False,
        default=0,
    )


class Process(models.Model):
    """
    过程
    """

    name = models.CharField(max_length=200, verbose_name="过程名称", null=False, blank=False)
    allocation_method = models.CharField(
        choices=ALLOCATION_METHODS.choices,
        max_length=50,
        verbose_name="分配方法",
        null=True,
        blank=False,
        default=None,
    )
    co2e = models.DecimalField(
        decimal_places=5,
        max_digits=20,
        verbose_name="CO2e",
        null=False,
        blank=False,
        default=0,
    )

    @property
    def inputs(self) -> QuerySet["Input"]: ...

    @property
    def outputs(self) -> QuerySet["Output"]: ...

    @property
    def input(self) -> "Input":
        return Input.objects.filter(process=self).first()

    @property
    def life_cycle(self) -> LifeCycle:
        return LifeCycle.objects.filter(process=self).first()


class Allocation(models.Model):
    """
    分配
    """

    percent = models.DecimalField(decimal_places=5, max_digits=20, verbose_name="百分比", null=True, blank=False)
    description = models.CharField(max_length=200, verbose_name="描述", null=True, blank=True)
    emission_source = models.ForeignKey(
        "EmissionSource",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="排放源",
    )
    custom_factor = models.ForeignKey(
        "CustomFactor",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name="自定义因子",
    )
    amount = models.DecimalField(max_digits=20, decimal_places=10, verbose_name="替代数量", null=True, blank=True)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, verbose_name="单位", null=True, blank=True)


class Output(models.Model):
    """
    输出
    """

    name = models.CharField(max_length=200, verbose_name="输出名称", null=True, blank=False)
    flow = models.ForeignKey("lca.Flow", on_delete=models.CASCADE, verbose_name="流", null=True, blank=True)
    process = models.ForeignKey(
        "Process",
        related_name="outputs",
        on_delete=models.CASCADE,
        null=False,
        blank=False,
    )
    type = models.CharField(
        choices=OUTPUT_TYPES.choices,
        max_length=50,
        verbose_name="输出类型",
        null=False,
        blank=False,
        default="main-product",
    )
    amount = models.DecimalField(decimal_places=5, max_digits=20, verbose_name="数量", null=False, blank=False)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, verbose_name="单位", null=False, blank=False)
    allocation = models.ForeignKey(Allocation, on_delete=models.CASCADE, verbose_name="分配", null=True, blank=True)
    emission_source = models.ForeignKey(
        EmissionSource,
        on_delete=models.CASCADE,
        verbose_name="回收排放源",
        null=True,
        blank=False,
    )
    custom_factor = models.ForeignKey(
        "CustomFactor",
        on_delete=models.CASCADE,
        verbose_name="自定义因子",
        null=True,
        blank=True,
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)

    def __str__(self):
        return f"{self.amount} {self.unit.id} {self.name}"


class Transport(models.Model):
    """
    模型原材料运输
    """

    input = models.ForeignKey(
        "Input",
        on_delete=models.CASCADE,
        verbose_name="原材料",
        null=False,
        blank=False,
        related_name="transports",
    )
    gross_weight = models.DecimalField(decimal_places=5, max_digits=20, verbose_name="毛重", null=False, blank=False)
    origin = models.CharField(max_length=100, verbose_name="起始地", null=True, blank=False)
    terminal = models.CharField(max_length=100, verbose_name="目的地", null=True, blank=False)
    emission_source = models.ForeignKey(
        EmissionSource,
        on_delete=models.CASCADE,
        verbose_name="排放源",
        null=False,
        blank=False,
    )
    distance = models.DecimalField(
        decimal_places=5,
        max_digits=20,
        verbose_name="运输距离",
        null=False,
        blank=False,
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)


class CustomFactor(models.Model):
    """
    自定义因子
    """

    source = models.CharField(
        choices=CUSTOM_FACTOR_SOURCE.choices,
        max_length=50,
        verbose_name="自定义因子数据来源来源",
        null=False,
        blank=False,
    )
    amount = models.DecimalField(
        decimal_places=5,
        max_digits=20,
        verbose_name="数量",
        null=False,
        blank=False,
        default=1,
    )
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, verbose_name="单位", null=False, blank=False)
    co2e = models.DecimalField(
        decimal_places=5,
        max_digits=20,
        verbose_name="二氧化碳当量",
        null=False,
        blank=False,
    )
    description = models.CharField(max_length=200, verbose_name="描述", null=True, blank=True)


class Input(models.Model):
    """
    模型输入
    """

    parent_process = models.ForeignKey(
        Process,
        related_name="inputs",
        on_delete=models.CASCADE,
        null=False,
        blank=False,
        db_index=True,
    )
    name = models.CharField(max_length=100, verbose_name="原材料名称", null=False, blank=False)
    type = models.CharField(
        choices=INPUT_TYPES.choices,
        max_length=50,
        verbose_name="原材料类型",
        null=False,
        blank=False,
    )
    amount = models.DecimalField(decimal_places=5, max_digits=20, verbose_name="用量", null=False, blank=False)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, verbose_name="单位", null=False, blank=False)
    source_type = models.CharField(
        choices=SOURCE_TYPES.choices,
        max_length=50,
        verbose_name="数据来源",
        null=False,
        blank=False,
    )
    emission_source = models.ForeignKey(
        EmissionSource,
        on_delete=models.CASCADE,
        verbose_name="排放源",
        null=True,
        blank=False,
    )
    process = models.ForeignKey(Process, on_delete=models.CASCADE, null=True, blank=False, db_index=True)
    custom_factor = models.ForeignKey(CustomFactor, on_delete=models.CASCADE, null=True, blank=False)
    co2e = models.DecimalField(
        decimal_places=5,
        max_digits=20,
        verbose_name="CO2e",
        null=False,
        blank=False,
        default=0,
    )
    create_time = models.DateTimeField(verbose_name="创建时间", auto_now_add=True)
    update_time = models.DateTimeField(verbose_name="更新时间", auto_now=True)
