from datetime import datetime
import random
import uuid
from lca.accounting.models import EmissionSource
from lca.common.categoryServices import CategoryServices
from django.template.loader import render_to_string
from ninja.errors import HttpError
from lca.database.models import EmissionSourceDataSet, EmissionSourceDataSetApplication, EmissionSourceInput, EmissionSourceInputApplication, EmissionSourceManagement, EmissionSourceManagementApplication, DATABASE_IMPORT_STATUS, EMISSION_SOURCE_STATUS, Database, DatabaseImportTask, EmissionSourceApplication, EmissionSourceOutput, EmissionSourceOutputApplication
from lca.database.schema import EmissionSourceInSchema
from lca.file.models import File
from django.db import transaction

from lca.users.models import User


class DatabaseService:
    @staticmethod
    def search(text: str = "", category_id: str | None = None, **kwargs):
        query = EmissionSource.objects.filter(**kwargs)
        if category_id:
            ids = CategoryServices.get_all_id(category_id)
            query = query.filter(category_id__in=ids)
        if text:
            query = query.filter(name__icontains=text)

        return query.all()

    @staticmethod
    def export_process_ilcd(process: EmissionSource):
        start, end = DatabaseService.get_emission_source_start_end(process.dataset.year)
        cats = []
        cat = process.dataset.category
        while cat:
            cats.insert(0, cat)
            cat = cat.parent
        data = dict(
            process=process,
            dataset=process.dataset,
            management=process.management,
            inputs=process.inputs.all(),
            outputs=process.outputs.all(),
            start=start.strftime("%Y"),
            end=end.strftime("%Y"),
            start_time=int(start.timestamp()) * 1000,
            end_time=int(end.timestamp()) * 1000,
            management_time=process.management.generate_create_time.isoformat(),
            cats=cats,
            create_time=process.management.generate_create_time.isoformat(),
            last_update=process.management.generate_update_time.isoformat(),
        )
        return render_to_string("process.json", data)

    @staticmethod
    def get_emission_source_start_end(year: str):
        """转换年份的开始和结束时间"""
        times = year.split("-")
        start = datetime(int(times[0]), 1, 1)
        end = datetime(int(times[len(times) - 1]), 12, 31)
        return start, end

    @staticmethod
    def import_database(name, file: File):
        # 数据库中有同名的且不为导入失败的，不可以导入
        if Database.objects.filter(name=name, status__in=[
                DATABASE_IMPORT_STATUS.PENDING,
                DATABASE_IMPORT_STATUS.IN_PROGRESS,
                DATABASE_IMPORT_STATUS.SUCCESS,
        ]).exists():
            raise HttpError(422, f"数据库中已存在同名数据{name}")
        database = Database.objects.create(name=name, file=file, status=DATABASE_IMPORT_STATUS.IN_PROGRESS)

        # 添加导入任务，进度随机0-99
        return DatabaseImportTask.objects.create(source=database, status=DATABASE_IMPORT_STATUS.IN_PROGRESS, import_time=datetime.now(), progress=random.randint(0, 99))

    @staticmethod
    def export_database(database: Database):
        return database.file

    @staticmethod
    def create_emission_source_application(user: User, data: EmissionSourceInSchema):
        application = EmissionSourceApplication(
            creator=user,
            amount=1,
            uuid=uuid.uuid4(),
        )
        return DatabaseService.update_emission_source_application(application, data)

    @staticmethod
    def update_emission_source_application(application: EmissionSourceApplication, data: EmissionSourceInSchema):
        with transaction.atomic():
            if application.id is not None:
                application = EmissionSourceApplication.objects.select_for_update().get(pk=application.id)
            if application.status != EMISSION_SOURCE_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            for key, value in data.model_dump(exclude=["dataset", "management", "inputs_outputs"]).items():
                print(f'==================={value}')
                setattr(application, key, value)
            application.name = data.inputs_outputs.name.dict()
            application.category_id = data.dataset.category_id
            application.geography_id = data.dataset.geography_id
            application.source = data.dataset.source.dict()
            application.year = data.dataset.year
            application.unit_id = data.inputs_outputs.unit_id
            application.co2e = data.inputs_outputs.co2e
            application.save()
            EmissionSourceManagementApplication.objects.update_or_create(
                emission_source=application,
                defaults=data.management.model_dump()
            )  # type: ignore
            EmissionSourceDataSetApplication.objects.update_or_create(
                emission_source=application,
                defaults=data.dataset.model_dump()
            )  # type: ignore
            # 删除 input 和 output
            EmissionSourceInputApplication.objects.filter(emission_source=application).delete()
            for item in data.inputs_outputs.inputs:
                print('===================', application, item.model_dump())
                EmissionSourceInputApplication.objects.create(emission_source=application, **item.model_dump())
            EmissionSourceOutputApplication.objects.filter(emission_source=application).delete()
            for item in data.inputs_outputs.outputs:
                EmissionSourceOutputApplication.objects.create(emission_source=application, **item.model_dump())
            return application

    @staticmethod
    def create_emission_source(user: User, data: EmissionSourceInSchema):
        emission_source = EmissionSource(
            creator=user,
            amount=1,
            uuid=uuid.uuid4(),
        )
        return DatabaseService.update_emission_source(emission_source, data)

    @staticmethod
    def update_emission_source(emission_source: EmissionSource, data: EmissionSourceInSchema):
        with transaction.atomic():
            if emission_source.id is not None:
                emission_source = EmissionSource.objects.select_for_update().get(pk=emission_source.id)

            for key, value in data.model_dump(exclude=["dataset", "management", "inputs_outputs"]).items():
                setattr(emission_source, key, value)
            emission_source.name = data.inputs_outputs.name.dict()
            emission_source.category_id = data.dataset.category_id
            emission_source.geography_id = data.dataset.geography_id
            emission_source.source = data.dataset.source.dict()
            emission_source.year = data.dataset.year
            emission_source.unit_id = data.inputs_outputs.unit_id
            emission_source.co2e = data.inputs_outputs.co2e
            emission_source.save()
            EmissionSourceManagement.objects.update_or_create(
                emission_source=emission_source,
                defaults=data.management.model_dump()
            )  # type: ignore
            EmissionSourceDataSet.objects.update_or_create(
                emission_source=emission_source,
                defaults=data.dataset.model_dump()
            )  # type: ignore
            # 删除 input 和 output
            EmissionSourceInput.objects.filter(emission_source=emission_source).delete()
            for item in data.inputs_outputs.inputs:
                EmissionSourceInput.objects.create(emission_source=emission_source, **item.model_dump())
            EmissionSourceOutput.objects.filter(emission_source=emission_source).delete()
            for item in data.inputs_outputs.outputs:
                EmissionSourceOutput.objects.create(emission_source=emission_source, **item.model_dump())
            return emission_source
