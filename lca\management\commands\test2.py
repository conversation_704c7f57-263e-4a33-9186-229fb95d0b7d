from django.core.management import BaseCommand
from infra.utils import label_to_value
import csv
from django.db import transaction

from lca.accounting.models import EmissionSource
from lca.database.models import FLOW_TYPES, EmissionSourceApplication, Flow


class Command(BaseCommand):
    help = "初始化流"

    def handle(self, *args, **options):
        run()


def run():
    EmissionSource.objects.all()
