# Generated by Django 5.2.1 on 2025-07-24 14:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lca', '0089_emissionsourceinput_flow_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='emissionsourceoutput',
            name='allocation',
            field=models.DecimalField(decimal_places=5, default=100, max_digits=20, verbose_name='分配占比'),
        ),
        migrations.AddField(
            model_name='emissionsourceoutput',
            name='flow_type',
            field=models.CharField(choices=[('elementary', '基本流'), ('intermediate', '中间流'), ('product', '产品流'), ('co-product', '共产品流'), ('waste', '废物流'), ('resource', '资源流')], default='elementary', max_length=255, verbose_name='流类型'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='emissionsourceoutput',
            name='type',
            field=models.CharField(choices=[('main-product', '主产品'), ('by-product', '共生产品'), ('environmental-emission', '直接排放'), ('waste', '待处置废弃物')], default='main-product', max_length=255, verbose_name='输出类型'),
            preserve_default=False,
        ),
    ]
